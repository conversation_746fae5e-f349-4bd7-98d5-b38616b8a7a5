// Targeted test for specific fixes implemented
const { GameDatabase } = require('./game-state-server/dist/db.js');

async function testSpecificFixes() {
  console.log('🎯 Testing Specific Implemented Fixes...\n');
  
  const db = new GameDatabase();
  let results = { passed: 0, failed: 0 };

  function logResult(testName, passed, details = '') {
    const status = passed ? '✅ PASS' : '❌ FAIL';
    console.log(`${status}: ${testName}`);
    if (details) console.log(`   ${details}`);
    if (passed) results.passed++; else results.failed++;
  }

  // Create test character
  const timestamp = Date.now();
  const testChar = db.createCharacter({
    name: `FixTest_${timestamp}`,
    concept: 'Fix Test',
    game_line: 'vampire',
    willpower_current: 3,
    willpower_permanent: 5,
    blood_pool_current: 8,
    blood_pool_max: 10,
    experience: 50,
    strength: 2
  });

  console.log('='.repeat(60));
  console.log('FIX 1: Resource Restoration Messaging');
  console.log('='.repeat(60));

  // Test accurate restoration messaging
  const restoreResult1 = db.atomicResourceOperation(testChar.id, 'willpower', 'restore', 2, 5);
  logResult('Accurate Restoration Amount (Partial)', 
    restoreResult1.success && restoreResult1.actualChange === 2,
    `Requested: 2, Actually restored: ${restoreResult1.actualChange}, New value: ${restoreResult1.newValue}`);

  const restoreResult2 = db.atomicResourceOperation(testChar.id, 'willpower', 'restore', 3, 5);
  logResult('Accurate Restoration Amount (At Max)', 
    restoreResult2.success && restoreResult2.actualChange === 0,
    `Requested: 3, Actually restored: ${restoreResult2.actualChange}, Value unchanged: ${restoreResult2.newValue}`);

  console.log('\n' + '='.repeat(60));
  console.log('FIX 2: State Staleness (WAL Checkpoint)');
  console.log('='.repeat(60));

  // Test immediate state consistency
  const beforeUpdate = db.getCharacter(testChar.id);
  const updateResult = db.updateCharacter(testChar.id, { experience: 100 });
  const afterUpdate = db.getCharacter(testChar.id);

  logResult('Immediate State Consistency After Update',
    beforeUpdate.experience !== afterUpdate.experience && 
    updateResult.experience === afterUpdate.experience && 
    afterUpdate.experience === 100,
    `Before: ${beforeUpdate.experience} -> Update: ${updateResult.experience} -> After: ${afterUpdate.experience}`);

  console.log('\n' + '='.repeat(60));
  console.log('FIX 3: Trait Improvement Implementation');
  console.log('='.repeat(60));

  // Test trait improvement (create fresh character to avoid locks)
  const traitChar = db.createCharacter({
    name: `TraitChar_${timestamp}`,
    concept: 'Trait Test',
    game_line: 'vampire',
    experience: 50,
    strength: 2
  });

  try {
    const improvementResult = db.improveTrait(traitChar.id, 'attribute', 'strength');
    const freshLookup = db.getCharacter(traitChar.id);
    
    logResult('Trait Improvement Returns Updated Character',
      improvementResult.updated_character && 
      improvementResult.updated_character.strength === improvementResult.new_rating,
      `New strength: ${improvementResult.new_rating}, XP cost: ${improvementResult.xp_cost}`);

    logResult('Trait Improvement Immediate Consistency',
      freshLookup.strength === improvementResult.new_rating &&
      freshLookup.experience === improvementResult.updated_character.experience,
      `Fresh lookup matches: strength=${freshLookup.strength}, XP=${freshLookup.experience}`);

  } catch (error) {
    logResult('Trait Improvement', false, error.message);
  }

  console.log('\n' + '='.repeat(60));
  console.log('FIX 4: Atomicity and Resource Operations');
  console.log('='.repeat(60));

  // Test atomic operations return correct change amounts
  const atomicChar = db.createCharacter({
    name: `AtomicChar_${timestamp}`,
    concept: 'Atomic Test',
    game_line: 'vampire',
    willpower_current: 5,
    willpower_permanent: 5
  });

  const spendResult = db.atomicResourceOperation(atomicChar.id, 'willpower', 'spend', 2);
  logResult('Atomic Spend Operation',
    spendResult.success && spendResult.oldValue === 5 && 
    spendResult.newValue === 3 && spendResult.actualChange === -2,
    `Old: ${spendResult.oldValue}, New: ${spendResult.newValue}, Change: ${spendResult.actualChange}`);

  const gainResult = db.atomicResourceOperation(atomicChar.id, 'willpower', 'restore', 1, 5);
  logResult('Atomic Restore Operation',
    gainResult.success && gainResult.oldValue === 3 && 
    gainResult.newValue === 4 && gainResult.actualChange === 1,
    `Old: ${gainResult.oldValue}, New: ${gainResult.newValue}, Change: ${gainResult.actualChange}`);

  console.log('\n' + '='.repeat(60));
  console.log('FIX 5: MCP Schema Compliance (Status Effects)');
  console.log('='.repeat(60));

  // Test status effect creation (this tests the schema fix)
  try {
    const effectId = db.addStatusEffect({
      target_type: 'character',
      target_id: testChar.id,
      effect_name: 'Test Effect',
      description: 'Test description',
      mechanical_effect: { test: true },
      duration_type: 'rounds',
      duration_value: 3
    });

    logResult('Status Effect Creation (Schema Fix)',
      typeof effectId === 'number' && effectId > 0,
      `Created effect ID: ${effectId}`);

    // Test status effect listing
    const effects = db.listStatusEffects('character', testChar.id);
    logResult('Status Effect Retrieval',
      Array.isArray(effects) && effects.length > 0,
      `Retrieved ${effects.length} effects`);

  } catch (error) {
    logResult('Status Effect Operations', false, error.message);
  }

  console.log('\n' + '='.repeat(60));
  console.log('SUMMARY');
  console.log('='.repeat(60));
  console.log(`Total Tests: ${results.passed + results.failed}`);
  console.log(`Passed: ${results.passed}`);
  console.log(`Failed: ${results.failed}`);
  console.log(`Success Rate: ${((results.passed / (results.passed + results.failed)) * 100).toFixed(1)}%`);

  if (results.failed === 0) {
    console.log('\n🎉 All specific fixes are working correctly!');
  } else {
    console.log(`\n⚠️  ${results.failed} tests still failing - may need additional investigation.`);
  }

  return results;
}

// Run the tests
if (require.main === module) {
  testSpecificFixes().catch(console.error);
}

module.exports = { testSpecificFixes };
