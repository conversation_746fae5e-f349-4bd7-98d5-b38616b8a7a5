// Exhaustive Test Runner: game-state-server
// Autogenerated for full coverage of TestingPlan.md, including positive, negative, edge, and integration cases with clear input/output expectation logs.

import * as path from "path";
import { promises as fs } from "fs";

// Import game-state-server tool handler
import { handleToolRequest } from "./index.js";

// Helper for MCP tool invocations, logs, dynamic state, and expected evaluation
async function callTool(tool: string, args: any, expectDesc: string, results: any[], state: any, prevIdKey?: string, storeIdKey?: string) {
  // Dynamic replacement for IDs generated earlier (by key)
  if (prevIdKey && state[prevIdKey]) {
    args[prevIdKey] = state[prevIdKey];
  }
  let output, pass, error, info = "";
  try {
    // Call the MCP handler directly with a compliant payload
    output = await handleToolRequest({ params: { name: tool, arguments: args } });
    pass = !!output && !output.isError;
    info = (output && output.content && output.content.map?.((c: any) => c.text).join('\n')) || JSON.stringify(output);
    // Store returned IDs if available
    if (storeIdKey && output && !output.isError) {
      // Try to parse numeric IDs out of output message (primitive but effective)
      const found = /ID: ?(\d+)/.exec(info) || /#(\d+)/.exec(info) || /character_id[": ]+(\d+)/.exec(info);
      if (found) state[storeIdKey] = parseInt(found[1]);
    }
  } catch (e: any) {
    output = { error: e.message };
    error = e;
    pass = false;
    info = e.message;
  }
  results.push({
    step: results.length + 1,
    tool,
    input: { ...args },
    expected: expectDesc,
    actual: info,
    pass,
  });
  return pass;
}

async function runAllTests() {
  const results: any[] = [];
  const state: any = {};
  // List of all output IDs/state across steps for chaining

  // -- CHARACTER MANAGEMENT --
  // Standard creation (Vampire)
  // Enhanced: Provide all minimally-required fields for Vampire and Werewolf (per schema and bug log)
  await callTool("create_character",
    {
      name: "Armand",
      game_line: "vampire",
      concept: "Artist",
      clan: "Toreador",
      strength: 2,
      dexterity: 2,
      stamina: 2,
      charisma: 3,
      manipulation: 2,
      appearance: 3,
      perception: 2,
      intelligence: 2,
      wits: 2,
      willpower_current: 5,
      willpower_permanent: 5,
      blood_pool_current: 10,
      blood_pool_max: 10,
      humanity: 7
    },
    "Character/NPC created and retrievable. Splat-specific tables populated.",
    results, state, undefined, "character_id");
  // Edge: Minimal input for Werewolf (now with all required fields)
  await callTool("create_character",
    {
      name: "Elsa",
      game_line: "werewolf",
      concept: "Warden",
      tribe: "Children of Gaia",
      auspice: "Philodox",
      strength: 3,
      dexterity: 3,
      stamina: 3,
      charisma: 2,
      manipulation: 2,
      appearance: 2,
      perception: 2,
      intelligence: 2,
      wits: 2,
      willpower_current: 4,
      willpower_permanent: 4,
      gnosis_current: 3,
      gnosis_permanent: 3,
      rage: 2
      // Add/change as DB schema demands (should match your real table's NOT NULLs/defaults)
    },
    "Character created with required fields/valid defaults for all omitted.",
    results, state, undefined, "character_id_wolf");
  // Validation: Missing required
  await callTool("create_character",
    { game_line: "mage" },
    "Error message: Missing required field: name.",
    results, state);
  // Validation: Invalid Enum
  await callTool("create_character",
    { name: "Test", game_line: "dragon" },
    "Error message: Invalid value for game_line.",
    results, state);
  // Negative: Duplicate name
  await callTool("create_character",
    { name: "Armand", game_line: "vampire" },
    "UNIQUE constraint failed error on second attempt.",
    results, state);

  // Get standard (by ID & Name)
  await callTool("get_character", { character_id: state.character_id }, "Full, correctly formatted character sheet is returned.", results, state);
  await callTool("get_character_by_name", { name: "Armand" }, "Full, correctly formatted character sheet is returned.", results, state);
  // Splat-specific (see Rage/Gnosis/Gifts for Elsa)
  await callTool("get_character", { character_id: state.character_id_wolf }, "Response includes Rage, Gnosis, Gifts, etc.", results, state);
  // Negative: Nonexistent
  await callTool("get_character", { character_id: 99999 }, 'Clear "Not Found" error message.', results, state);
  // Validation: Invalid type
  await callTool("get_character", { character_id: "abc" }, "Input validation error.", results, state);

  // Update character
  await callTool("update_character", { character_id: state.character_id, updates: { concept: "Survivor" } }, "Success confirmation. get_character reflects the change.", results, state);
  await callTool("update_character", { character_id: state.character_id, updates: { humanity: 6 } }, "Success confirmation. get_character shows new humanity.", results, state);
  await callTool("update_character", { character_id: state.character_id, updates: { luck_points: 5 } }, "Error message: Invalid field 'luck_points'.", results, state);
  await callTool("update_character", { character_id: state.character_id, updates: { strength: "strong" } }, "Input validation error.", results, state);

  // -- RESOURCE/HEALTH/PROGRESSION --
  // Standard Spend/Restore
  await callTool("spend_resource", { character_id: state.character_id, resource_name: "willpower", amount: 1 }, "Success, new/max values.", results, state);
  await callTool("restore_resource", { character_id: state.character_id, resource_name: "willpower", amount: 1 }, "Restores/caps at max.", results, state);
  // Validation: Insufficient
  await callTool("spend_resource", { character_id: state.character_id, resource_name: "willpower", amount: 99 }, "Error: Not enough willpower.", results, state);
  // Over-restoring
  await callTool("restore_resource", { character_id: state.character_id, resource_name: "willpower", amount: 99 }, "Capped at maximum.", results, state);
  // Invalid resource type (Vampire)
  await callTool("spend_resource", { character_id: state.character_id, resource_name: "gnosis", amount: 1 }, "Error: Invalid resource for game_line.", results, state);

  // Gaining resources (positive and invalid)
  await callTool("gain_resource", { character_id: state.character_id, resource_name: "blood", roll_successes: 3 }, "Success; blood pool increases.", results, state);
  await callTool("gain_resource", { character_id: state.character_id, resource_name: "gnosis", roll_successes: 2 }, "Error; not applicable to Vampire.", results, state);
  await callTool("gain_resource", { character_id: state.character_id, resource_name: "blood", roll_successes: 0 }, "Error: roll_successes must be positive.", results, state);

  // Health/damage (Bashing→Lethal upgrade)
  await callTool("apply_damage", { target_type: "character", target_id: state.character_id, damage_successes: 2, damage_type: "bashing" }, "Bashing applied.", results, state);
  await callTool("apply_damage", { target_type: "character", target_id: state.character_id, damage_successes: 1, damage_type: "lethal" }, "Bashing upgrades to lethal.", results, state);
  await callTool("apply_damage", { target_type: "character", target_id: state.character_id, damage_successes: 8, damage_type: "lethal" }, "Track is full; Incapacitated.", results, state);

  // -- XP/PROGRESSION --
  await callTool("award_xp", { character_id: state.character_id, amount: 5, reason: "Story completion" }, "XP awarded, can be checked.", results, state);
  await callTool("get_trait_improvement_cost", { character_id: state.character_id, trait_type: "attribute", trait_name: "strength" }, "Correct cost for attribute.", results, state);
  await callTool("improve_trait", { character_id: state.character_id, trait_type: "attribute", trait_name: "strength" }, "Trait improved, XP deducted.", results, state);
  await callTool("spend_xp", { character_id: state.character_id, amount: 99, reason: "Overspend", trait_name: "strength" }, "Error: Not enough XP.", results, state);
  await callTool("improve_trait", { character_id: state.character_id, trait_type: "attribute", trait_name: "fake_trait" }, "Error: Trait not found.", results, state);

  // -- STATUS EFFECTS --
  await callTool("apply_status_effect", { target_type: "character", target_id: state.character_id, effect_name: "Cursed" }, "Status effect applied.", results, state, undefined, "effect_id");
  await callTool("remove_status_effect", { effect_id: state.effect_id }, "Status effect removed.", results, state);
  await callTool("get_status_effects", { target_type: "character", target_id: state.character_id }, "Returns all status effects.", results, state);

  // -- INVENTORY --
  await callTool("add_item", { character_id: state.character_id, item: { item_name: "Katana", quantity: 1 } }, "Item added.", results, state, undefined, "item_id");
  await callTool("get_inventory", { character_id: state.character_id }, "Should list the Katana.", results, state);
  await callTool("update_item", { item_id: state.item_id, updates: { quantity: 2 } }, "Item updated.", results, state);
  await callTool("remove_item", { item_id: state.item_id }, "Item removed.", results, state);

  // -- WORLD/ROSTER/PERSISTENCE/INTEGRATION --
  await callTool("save_world_state", { location: "Paris", notes: "XP Test", data: { foo: 123 } }, "World state saved.", results, state);
  await callTool("get_world_state", {}, "World state retrieved.", results, state);
  await callTool("save_story_progress", { chapter: "1", scene: "intro", summary: "Begin" }, "Story checkpoint saved.", results, state);
  // Antagonist: fill required fields if template demands them
  await callTool("create_antagonist",
    {
      template_name: "Sabbat Shovelhead",
      custom_name: "Rocco",
      concept: "Brute",
      strength: 3,
      dexterity: 2,
      stamina: 3,
      willpower_current: 3,
      willpower_permanent: 3
    }, "Antagonist created.", results, state, undefined, "npc_id");
  await callTool("get_antagonist", { npc_id: state.npc_id }, "Antagonist retrieved.", results, state);
  await callTool("update_antagonist", { npc_id: state.npc_id, updates: { strength: 5 } }, "Antagonist updated.", results, state);
  await callTool("list_antagonists", {}, "Roster of antagonists.", results, state);
  await callTool("remove_antagonist", { npc_id: state.npc_id }, "Antagonist removed.", results, state);
  await callTool("list_characters", {}, "Roster of all characters.", results, state);

  // -- INITIATIVE/COMBAT turn (minimum cases only, robust chains can be appended) --
  const testSceneId = "scene-1";
  await callTool("set_initiative", {
    scene_id: testSceneId,
    entries: [
      { character_id: state.character_id, actor_name: "Armand", initiative_score: 10, turn_order: 1 },
      { npc_id: state.npc_id || 1, actor_name: "Demon", initiative_score: 5, turn_order: 2 }
    ]
  }, "Initiative set with PC+NPC.", results, state);
  await callTool("get_initiative_order", { scene_id: testSceneId }, "Order returned.", results, state);
  await callTool("advance_turn", { scene_id: testSceneId }, "Advances the turn.", results, state);
  await callTool("get_current_turn", { scene_id: testSceneId }, "Gets actor/round for turn.", results, state);

  // Summarize and save results
  const outputFile = "test-game-state-server-results.json";
  await fs.writeFile(outputFile, JSON.stringify(results, null, 2));
  console.log("Exhaustive Game State Server Testing Complete: See " + outputFile);
}

runAllTests()
  .catch(e => {
    console.error(e);
    process.exit(1);
  });