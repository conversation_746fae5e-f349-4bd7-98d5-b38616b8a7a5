// Comprehensive test script for all implemented fixes
// Tests: MCP schema fixes, resource messaging, state staleness, level-up propagation, lock error messages

const { GameDatabase } = require('./game-state-server/dist/db.js');

async function testAllFixes() {
  console.log('🧪 Testing All Implemented Fixes...\n');
  console.log('=' .repeat(60));
  
  const db = new GameDatabase();
  let testResults = {
    passed: 0,
    failed: 0,
    details: []
  };

  function logTest(testName, passed, details = '') {
    const status = passed ? '✅ PASS' : '❌ FAIL';
    console.log(`${status}: ${testName}`);
    if (details) console.log(`   ${details}`);
    
    testResults.details.push({ testName, passed, details });
    if (passed) testResults.passed++;
    else testResults.failed++;
  }

  // Create test character
  const timestamp = Date.now();
  let testChar;
  try {
    testChar = db.createCharacter({
      name: `TestChar_${timestamp}`,
      concept: 'Test Subject',
      game_line: 'vampire',
      willpower_current: 3,
      willpower_permanent: 5,
      blood_pool_current: 8,
      blood_pool_max: 10,
      experience: 50
    });
    logTest('Character Creation', !!testChar, `Created character ID: ${testChar?.id}`);
  } catch (error) {
    logTest('Character Creation', false, error.message);
    return testResults;
  }

  console.log('\n' + '=' .repeat(60));
  console.log('TEST BLOCK 1: Resource Restoration Messaging Fix');
  console.log('=' .repeat(60));

  // Test 1.1: Resource restoration when not at max
  try {
    const result = db.atomicResourceOperation(testChar.id, 'willpower', 'restore', 2, 5);
    const actualRestored = result.actualChange || 0;
    logTest('Resource Restoration (Not at Max)', 
      result.success && actualRestored === 2, 
      `Restored ${actualRestored}/2 willpower, new value: ${result.newValue}`);
  } catch (error) {
    logTest('Resource Restoration (Not at Max)', false, error.message);
  }

  // Test 1.2: Resource restoration when already at max
  try {
    const result = db.atomicResourceOperation(testChar.id, 'willpower', 'restore', 3, 5);
    const actualRestored = result.actualChange || 0;
    logTest('Resource Restoration (At Max)', 
      result.success && actualRestored === 0, 
      `Restored ${actualRestored}/3 willpower (already at max), value: ${result.newValue}`);
  } catch (error) {
    logTest('Resource Restoration (At Max)', false, error.message);
  }

  console.log('\n' + '=' .repeat(60));
  console.log('TEST BLOCK 2: Resource Lock Error Messages');
  console.log('=' .repeat(60));

  // Test 2.1: Improved lock error messages
  try {
    // Create a separate test character to avoid lock conflicts
    const lockTestChar = db.createCharacter({
      name: `LockTest_${timestamp}`,
      concept: 'Lock Test',
      game_line: 'vampire',
      blood_pool_current: 10,
      blood_pool_max: 10
    });

    // Simulate concurrent operations to trigger lock
    const promises = [];
    for (let i = 0; i < 2; i++) {
      promises.push(
        new Promise((resolve) => {
          const result = db.atomicResourceOperation(lockTestChar.id, 'blood', 'spend', 1);
          resolve(result);
        })
      );
    }

    Promise.all(promises).then((results) => {
      const hasSuccess = results.some(r => r.success);
      const hasLockError = results.some(r => !r.success && r.error && r.error.includes('locked by'));

      logTest('Improved Lock Error Messages',
        hasSuccess && hasLockError,
        `One success, one lock error: ${results.map(r => r.success ? 'SUCCESS' : 'LOCKED').join(', ')}`);
    });

    logTest('Lock Test Setup', !!lockTestChar, `Created lock test character ID: ${lockTestChar.id}`);
  } catch (error) {
    logTest('Resource Lock Error Test', false, error.message);
  }

  console.log('\n' + '=' .repeat(60));
  console.log('TEST BLOCK 3: State Staleness Fix');
  console.log('=' .repeat(60));

  // Test 3.1: Immediate state consistency after updates
  try {
    const beforeUpdate = db.getCharacter(testChar.id);
    const updateResult = db.updateCharacter(testChar.id, { experience: 75 });
    const afterUpdate = db.getCharacter(testChar.id);
    
    const stateConsistent = afterUpdate.experience === 75 && 
      updateResult.experience === 75;
    
    logTest('Immediate State Consistency', 
      stateConsistent,
      `Before: ${beforeUpdate.experience}, After update: ${updateResult.experience}, Fresh lookup: ${afterUpdate.experience}`);
  } catch (error) {
    logTest('Immediate State Consistency', false, error.message);
  }

  // Test 3.2: Rapid successive updates
  try {
    const updates = [];
    for (let i = 0; i < 3; i++) {
      const result = db.updateCharacter(testChar.id, { experience: 80 + i });
      updates.push(result.experience);
      
      // Immediate lookup after each update
      const lookup = db.getCharacter(testChar.id);
      updates.push(lookup.experience);
    }
    
    const allConsistent = updates.every((exp, index) => {
      if (index % 2 === 0) return exp === 80 + Math.floor(index / 2);
      return exp === updates[index - 1];
    });
    
    logTest('Rapid Successive Updates', 
      allConsistent,
      `Update sequence: ${updates.join(' -> ')}`);
  } catch (error) {
    logTest('Rapid Successive Updates', false, error.message);
  }

  console.log('\n' + '=' .repeat(60));
  console.log('TEST BLOCK 4: Level-up Propagation Fix');
  console.log('=' .repeat(60));

  // Test 4.1: Trait improvement with immediate propagation
  try {
    // Create a separate character for trait improvement to avoid lock conflicts
    const traitTestChar = db.createCharacter({
      name: `TraitTest_${timestamp}`,
      concept: 'Trait Test',
      game_line: 'vampire',
      experience: 100,
      strength: 2
    });

    const beforeImprovement = db.getCharacter(traitTestChar.id);
    const improvementResult = db.improveTrait(traitTestChar.id, 'attribute', 'strength');
    const afterImprovement = db.getCharacter(traitTestChar.id);

    const traitUpdated = improvementResult.updated_character.strength === improvementResult.new_rating;
    const xpDeducted = improvementResult.updated_character.experience === (100 - improvementResult.xp_cost);
    const immediateConsistency = afterImprovement.strength === improvementResult.new_rating &&
      afterImprovement.experience === improvementResult.updated_character.experience;

    logTest('Trait Improvement Propagation',
      traitUpdated && xpDeducted && immediateConsistency,
      `Strength: ${beforeImprovement.strength} -> ${improvementResult.new_rating}, XP: ${beforeImprovement.experience} -> ${improvementResult.updated_character.experience}`);
  } catch (error) {
    logTest('Trait Improvement Propagation', false, error.message);
  }

  console.log('\n' + '=' .repeat(60));
  console.log('TEST BLOCK 5: Atomicity and Race Condition Fixes');
  console.log('=' .repeat(60));

  // Test 5.1: Concurrent resource operations
  try {
    // Reset character resources
    db.updateCharacter(testChar.id, { willpower_current: 5 });
    
    const promises = [];
    const results = [];
    
    // Simulate concurrent operations
    for (let i = 0; i < 3; i++) {
      promises.push(
        new Promise((resolve) => {
          setTimeout(() => {
            const result = db.atomicResourceOperation(testChar.id, 'willpower', 'spend', 1);
            resolve(result);
          }, Math.random() * 50);
        })
      );
    }
    
    Promise.all(promises).then((concurrentResults) => {
      const successCount = concurrentResults.filter(r => r.success).length;
      const finalState = db.getCharacter(testChar.id);
      const expectedWillpower = 5 - successCount;
      
      logTest('Concurrent Resource Operations', 
        finalState.willpower_current === expectedWillpower,
        `${successCount} successful operations, final willpower: ${finalState.willpower_current}/${expectedWillpower}`);
    });
    
  } catch (error) {
    logTest('Concurrent Resource Operations', false, error.message);
  }

  // Wait a bit for async operations to complete
  setTimeout(() => {
    console.log('\n' + '=' .repeat(60));
    console.log('TEST SUMMARY');
    console.log('=' .repeat(60));
    console.log(`Total Tests: ${testResults.passed + testResults.failed}`);
    console.log(`Passed: ${testResults.passed}`);
    console.log(`Failed: ${testResults.failed}`);
    console.log(`Success Rate: ${((testResults.passed / (testResults.passed + testResults.failed)) * 100).toFixed(1)}%`);
    
    if (testResults.failed > 0) {
      console.log('\nFailed Tests:');
      testResults.details.filter(t => !t.passed).forEach(test => {
        console.log(`❌ ${test.testName}: ${test.details}`);
      });
    }
    
    console.log('\n🎉 All fixes testing completed!');
  }, 2000);

  return testResults;
}

// Run the tests
if (require.main === module) {
  testAllFixes().catch(console.error);
}

module.exports = { testAllFixes };
